import sqlite3
import os
import shutil
from datetime import datetime

def advanced_repair_database(db_path):
    print(f"正在尝试高级修复数据库: {db_path}")
    
    # 创建备份
    backup_path = f"{db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    shutil.copy2(db_path, backup_path)
    print(f"已创建备份: {backup_path}")
    
    # 方法1: 尝试使用只读模式导出数据
    print("\n=== 方法1: 尝试只读模式导出数据 ===")
    try:
        conn = sqlite3.connect(f"file:{db_path}?mode=ro", uri=True)
        cursor = conn.cursor()
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print(f"只读模式下找到的表: {[table[0] for table in tables]}")
        
        if tables:
            export_data_from_readonly(db_path, tables)
        
        conn.close()
        
    except Exception as e:
        print(f"只读模式失败: {e}")
    
    # 方法2: 尝试使用dump和restore
    print("\n=== 方法2: 尝试dump和restore ===")
    try:
        dump_file = f"{db_path}.dump"
        
        # 创建dump文件
        with open(dump_file, 'w', encoding='utf-8') as f:
            conn = sqlite3.connect(db_path)
            for line in conn.iterdump():
                f.write(line + '\n')
            conn.close()
        
        print(f"已创建dump文件: {dump_file}")
        
        # 从dump文件恢复
        new_db_path = f"{db_path}.restored"
        conn = sqlite3.connect(new_db_path)
        
        with open(dump_file, 'r', encoding='utf-8') as f:
            sql_script = f.read()
            conn.executescript(sql_script)
        
        conn.close()
        
        print(f"已从dump文件恢复数据库: {new_db_path}")
        
        # 验证恢复的数据库
        try:
            conn_test = sqlite3.connect(new_db_path)
            cursor_test = conn_test.cursor()
            cursor_test.execute("PRAGMA integrity_check;")
            integrity_result = cursor_test.fetchone()
            print(f"恢复的数据库完整性检查: {integrity_result}")
            conn_test.close()
            
            if integrity_result[0] == "ok":
                print("恢复的数据库完整性检查通过！")
                # 替换原数据库
                shutil.move(db_path, f"{db_path}.corrupted")
                shutil.move(new_db_path, db_path)
                print(f"已用恢复的数据库替换原数据库")
                return True
                
        except Exception as e:
            print(f"验证恢复的数据库时出错: {e}")
        
    except Exception as e:
        print(f"dump和restore失败: {e}")
    
    # 方法3: 尝试逐表恢复
    print("\n=== 方法3: 尝试逐表恢复 ===")
    try:
        new_db_path = f"{db_path}.repaired"
        
        # 尝试从损坏的数据库复制数据到新数据库
        conn_old = sqlite3.connect(db_path)
        conn_new = sqlite3.connect(new_db_path)
        
        cursor_old = conn_old.cursor()
        cursor_new = conn_new.cursor()
        
        cursor_old.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor_old.fetchall()
        
        for table in tables:
            table_name = table[0]
            try:
                print(f"正在处理表: {table_name}")
                
                # 获取表结构
                cursor_old.execute(f"PRAGMA table_info({table_name});")
                columns = cursor_old.fetchall()
                
                # 创建表结构
                create_sql = f"CREATE TABLE {table_name} ("
                column_defs = []
                for col in columns:
                    col_name = col[1]
                    col_type = col[2]
                    col_notnull = col[3]
                    col_default = col[4]
                    col_pk = col[5]
                    
                    col_def = f"{col_name} {col_type}"
                    if col_notnull:
                        col_def += " NOT NULL"
                    if col_default is not None:
                        col_def += f" DEFAULT {col_default}"
                    if col_pk:
                        col_def += " PRIMARY KEY"
                    
                    column_defs.append(col_def)
                
                create_sql += ", ".join(column_defs) + ");"
                cursor_new.execute(create_sql)
                
                # 尝试复制数据
                try:
                    cursor_old.execute(f"SELECT * FROM {table_name};")
                    rows = cursor_old.fetchall()
                    print(f"表 {table_name} 有 {len(rows)} 行数据")
                    
                    if rows:
                        placeholders = ", ".join(["?" for _ in columns])
                        insert_sql = f"INSERT INTO {table_name} VALUES ({placeholders});"
                        cursor_new.executemany(insert_sql, rows)
                        
                except Exception as e:
                    print(f"复制表 {table_name} 数据时出错: {e}")
                
            except Exception as e:
                print(f"处理表 {table_name} 时出错: {e}")
        
        conn_new.commit()
        conn_old.close()
        conn_new.close()
        
        print(f"已创建修复后的数据库: {new_db_path}")
        
        # 验证新数据库
        try:
            conn_test = sqlite3.connect(new_db_path)
            cursor_test = conn_test.cursor()
            cursor_test.execute("PRAGMA integrity_check;")
            integrity_result = cursor_test.fetchone()
            print(f"新数据库完整性检查: {integrity_result}")
            conn_test.close()
            
            if integrity_result[0] == "ok":
                print("新数据库完整性检查通过！")
                # 替换原数据库
                shutil.move(db_path, f"{db_path}.corrupted")
                shutil.move(new_db_path, db_path)
                print(f"已用修复后的数据库替换原数据库")
                return True
                
        except Exception as e:
            print(f"验证新数据库时出错: {e}")
        
    except Exception as e:
        print(f"逐表恢复失败: {e}")
    
    return False

def export_data_from_readonly(db_path, tables):
    """从只读模式导出数据"""
    try:
        conn = sqlite3.connect(f"file:{db_path}?mode=ro", uri=True)
        cursor = conn.cursor()
        
        export_file = f"{db_path}.export.sql"
        with open(export_file, 'w', encoding='utf-8') as f:
            for table in tables:
                table_name = table[0]
                try:
                    # 获取表结构
                    cursor.execute(f"PRAGMA table_info({table_name});")
                    columns = cursor.fetchall()
                    
                    # 写入CREATE TABLE语句
                    create_sql = f"CREATE TABLE {table_name} ("
                    column_defs = []
                    for col in columns:
                        col_name = col[1]
                        col_type = col[2]
                        col_notnull = col[3]
                        col_default = col[4]
                        col_pk = col[5]
                        
                        col_def = f"{col_name} {col_type}"
                        if col_notnull:
                            col_def += " NOT NULL"
                        if col_default is not None:
                            col_def += f" DEFAULT {col_default}"
                        if col_pk:
                            col_def += " PRIMARY KEY"
                        
                        column_defs.append(col_def)
                    
                    create_sql += ", ".join(column_defs) + ");"
                    f.write(create_sql + "\n")
                    
                    # 写入数据
                    cursor.execute(f"SELECT * FROM {table_name};")
                    rows = cursor.fetchall()
                    
                    for row in rows:
                        values = []
                        for value in row:
                            if value is None:
                                values.append("NULL")
                            elif isinstance(value, str):
                                # 处理字符串中的单引号
                                escaped_value = value.replace("'", "''")
                                values.append(f"'{escaped_value}'")
                            else:
                                values.append(str(value))
                        
                        insert_sql = f"INSERT INTO {table_name} VALUES ({', '.join(values)});"
                        f.write(insert_sql + "\n")
                    
                    f.write("\n")
                    
                except Exception as e:
                    print(f"导出表 {table_name} 时出错: {e}")
        
        conn.close()
        print(f"已导出数据到: {export_file}")
        
    except Exception as e:
        print(f"导出数据失败: {e}")

if __name__ == "__main__":
    success = advanced_repair_database("user.db")
    if success:
        print("\n数据库修复成功！")
    else:
        print("\n数据库修复失败，建议使用专业工具或从备份恢复") 