import sqlite3

def verify_database(db_path):
    print(f"正在验证数据库: {db_path}")
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 执行完整性检查
        print("执行完整性检查...")
        cursor.execute("PRAGMA integrity_check;")
        integrity_result = cursor.fetchone()
        print(f"完整性检查结果: {integrity_result}")
        
        if integrity_result[0] != "ok":
            print("❌ 数据库完整性检查失败")
            return False
        
        # 获取所有表
        print("\n获取数据库表列表...")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print(f"数据库中的表: {[table[0] for table in tables]}")
        
        # 检查每个表的数据
        print("\n检查各表数据:")
        total_rows = 0
        for table in tables:
            table_name = table[0]
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table_name};")
                count = cursor.fetchone()[0]
                print(f"  {table_name}: {count} 行")
                total_rows += count
            except Exception as e:
                print(f"  {table_name}: 检查失败 - {e}")
        
        print(f"\n总行数: {total_rows}")
        
        # 特别检查SiteStatistic表（这是出错的地方）
        print("\n特别检查SiteStatistic表:")
        try:
            cursor.execute("SELECT * FROM sitestatistic LIMIT 5;")
            rows = cursor.fetchall()
            print(f"SiteStatistic表前5行数据:")
            for i, row in enumerate(rows, 1):
                print(f"  行{i}: {row}")
        except Exception as e:
            print(f"SiteStatistic表检查失败: {e}")
        
        conn.close()
        print("\n✅ 数据库验证完成，数据库正常工作！")
        return True
        
    except Exception as e:
        print(f"❌ 数据库验证失败: {e}")
        return False

if __name__ == "__main__":
    success = verify_database("user.db")
    if success:
        print("\n🎉 数据库修复成功！MoviePilot应该可以正常使用了。")
    else:
        print("\n�� 数据库仍有问题，需要进一步处理。") 