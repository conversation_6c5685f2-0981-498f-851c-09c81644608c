import sqlite3
import os

def compare_databases(original_db, repaired_db):
    print("=== 数据库比较分析 ===\n")
    
    # 检查原始数据库
    print("原始数据库分析:")
    try:
        conn_orig = sqlite3.connect(original_db)
        cursor_orig = conn_orig.cursor()
        
        # 获取表列表
        cursor_orig.execute("SELECT name FROM sqlite_master WHERE type='table';")
        orig_tables = cursor_orig.fetchall()
        print(f"原始数据库表数量: {len(orig_tables)}")
        
        # 统计原始数据库数据
        orig_total_rows = 0
        orig_table_info = {}
        for table in orig_tables:
            table_name = table[0]
            try:
                cursor_orig.execute(f"SELECT COUNT(*) FROM {table_name};")
                count = cursor_orig.fetchone()[0]
                orig_table_info[table_name] = count
                orig_total_rows += count
                print(f"  {table_name}: {count} 行")
            except Exception as e:
                print(f"  {table_name}: 读取失败 - {e}")
                orig_table_info[table_name] = 0
        
        print(f"原始数据库总行数: {orig_total_rows}")
        conn_orig.close()
        
    except Exception as e:
        print(f"原始数据库分析失败: {e}")
        return
    
    print("\n" + "="*50 + "\n")
    
    # 检查修复后的数据库
    print("修复后数据库分析:")
    try:
        conn_rep = sqlite3.connect(repaired_db)
        cursor_rep = conn_rep.cursor()
        
        # 获取表列表
        cursor_rep.execute("SELECT name FROM sqlite_master WHERE type='table';")
        rep_tables = cursor_rep.fetchall()
        print(f"修复后数据库表数量: {len(rep_tables)}")
        
        # 统计修复后数据库数据
        rep_total_rows = 0
        rep_table_info = {}
        for table in rep_tables:
            table_name = table[0]
            try:
                cursor_rep.execute(f"SELECT COUNT(*) FROM {table_name};")
                count = cursor_rep.fetchone()[0]
                rep_table_info[table_name] = count
                rep_total_rows += count
                print(f"  {table_name}: {count} 行")
            except Exception as e:
                print(f"  {table_name}: 读取失败 - {e}")
                rep_table_info[table_name] = 0
        
        print(f"修复后数据库总行数: {rep_total_rows}")
        conn_rep.close()
        
    except Exception as e:
        print(f"修复后数据库分析失败: {e}")
        return
    
    print("\n" + "="*50 + "\n")
    
    # 比较分析
    print("数据差异分析:")
    
    # 检查缺失的表
    orig_table_names = set([table[0] for table in orig_tables])
    rep_table_names = set([table[0] for table in rep_tables])
    
    missing_tables = orig_table_names - rep_table_names
    if missing_tables:
        print(f"❌ 缺失的表: {list(missing_tables)}")
    else:
        print("✅ 所有表都保留")
    
    # 检查数据行数差异
    print(f"\n数据行数对比:")
    print(f"原始数据库: {orig_total_rows} 行")
    print(f"修复后数据库: {rep_total_rows} 行")
    print(f"差异: {orig_total_rows - rep_total_rows} 行")
    
    # 详细比较每个表
    print(f"\n各表数据对比:")
    all_tables = orig_table_names.union(rep_table_names)
    
    for table_name in sorted(all_tables):
        orig_count = orig_table_info.get(table_name, 0)
        rep_count = rep_table_info.get(table_name, 0)
        
        if orig_count != rep_count:
            diff = orig_count - rep_count
            if diff > 0:
                print(f"  ❌ {table_name}: 丢失 {diff} 行 (原始: {orig_count}, 修复后: {rep_count})")
            else:
                print(f"  ⚠️  {table_name}: 增加 {abs(diff)} 行 (原始: {orig_count}, 修复后: {rep_count})")
        else:
            print(f"  ✅ {table_name}: 无变化 ({orig_count} 行)")
    
    # 检查文件大小
    orig_size = os.path.getsize(original_db) / (1024*1024)
    rep_size = os.path.getsize(repaired_db) / (1024*1024)
    
    print(f"\n文件大小对比:")
    print(f"原始数据库: {orig_size:.1f} MB")
    print(f"修复后数据库: {rep_size:.1f} MB")
    print(f"大小减少: {orig_size - rep_size:.1f} MB ({((orig_size - rep_size) / orig_size * 100):.1f}%)")

if __name__ == "__main__":
    compare_databases("user.db.original", "user.db") 